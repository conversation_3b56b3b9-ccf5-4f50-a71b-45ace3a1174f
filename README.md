# QR-Code Based Student Attendance and Monitoring System

A modern, responsive web application built with Next.js 14 and TypeScript for Tanauan School of Arts and Trade (TSAT) located in Brgy. Cabuyan, Tanauan, Leyte.

## Features

### 🎯 Core Functionality
- **QR Code Scanning**: Real-time student attendance tracking via QR codes
- **Student Management**: Comprehensive student database with profiles and records
- **Analytics Dashboard**: Visual insights into attendance patterns and trends
- **Report Generation**: Automated and custom attendance reports
- **Settings Management**: Configurable system preferences and school information

### 🎨 Design & UI
- **Modern Interface**: Clean, professional design with shadcn/ui components
- **Dark/Light Theme**: Automatic theme switching with user preference
- **Responsive Design**: Optimized for tablet and desktop use
- **Educational Theme**: Blue and green color scheme representing education and technology
- **School Branding**: Integrated TSAT branding and identity

### 🛠 Technical Features
- **Next.js 14**: Latest React framework with App Router
- **TypeScript**: Full type safety throughout the application
- **Tailwind CSS**: Utility-first CSS framework for styling
- **Lucide React**: Beautiful, customizable icons
- **Error Boundaries**: Comprehensive error handling and recovery
- **Loading States**: Smooth loading experiences across all components

## Navigation Structure

- **Dashboard** (🏠): Overview of attendance statistics and recent activity
- **Scan QR Code** (📱): Real-time QR code scanning interface
- **Students** (👥): Student directory and management
- **Analytics** (📊): Attendance analytics and insights
- **Reports** (📄): Report generation and management
- **Settings** (⚙️): System configuration and preferences

## Getting Started

### Prerequisites
- Node.js 18+ 
- npm, yarn, or pnpm

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd qrsams
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## Project Structure

```
qrsams/
├── app/                    # Next.js 14 App Router
│   ├── analytics/         # Analytics page
│   ├── reports/           # Reports page
│   ├── scan/              # QR scanning page
│   ├── settings/          # Settings page
│   ├── students/          # Students management page
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   ├── page.tsx           # Dashboard page
│   ├── loading.tsx        # Global loading component
│   ├── error.tsx          # Global error component
│   └── not-found.tsx      # 404 page
├── components/            # Reusable components
│   ├── layout/           # Layout components
│   ├── ui/               # shadcn/ui components
│   ├── error-boundary.tsx # Error boundary component
│   ├── school-branding.tsx # School branding components
│   ├── theme-provider.tsx # Theme provider
│   └── theme-toggle.tsx   # Theme toggle component
├── lib/                   # Utility functions
│   └── utils.ts          # Common utilities
├── types/                 # TypeScript type definitions
│   └── index.ts          # Main type definitions
├── public/               # Static assets
├── tailwind.config.js    # Tailwind CSS configuration
├── tsconfig.json         # TypeScript configuration
├── next.config.js        # Next.js configuration
├── package.json          # Dependencies and scripts
└── README.md            # Project documentation
```

## Key Components

### Layout System
- **MainLayout**: Primary application layout with sidebar and header
- **Sidebar**: Navigation sidebar with responsive mobile support
- **Header**: Top navigation bar with theme toggle and user actions

### UI Components
- **Cards**: Information display containers
- **Buttons**: Interactive elements with multiple variants
- **Loading States**: Skeleton loaders and spinners
- **Error Boundaries**: Graceful error handling

### Theme System
- **ThemeProvider**: Next-themes integration for dark/light mode
- **Custom Colors**: Educational blue and green color palette
- **Responsive Design**: Mobile-first approach with tablet/desktop optimization

## TypeScript Interfaces

The application includes comprehensive TypeScript interfaces for:
- **Student**: Student information and records
- **Attendance**: Attendance tracking data
- **User**: System user management
- **Analytics**: Data visualization structures
- **Settings**: Configuration options

## Styling Approach

### Tailwind CSS Configuration
- Custom color palette for educational theme
- Extended spacing and typography scales
- Dark mode support with CSS variables
- Animation utilities for smooth interactions

### Component Styling
- Consistent design system with shadcn/ui
- Responsive breakpoints for all screen sizes
- Accessibility-focused color contrasts
- Professional educational aesthetic

## Development Guidelines

### Code Organization
- Feature-based component organization
- Consistent naming conventions
- TypeScript strict mode enabled
- ESLint configuration for code quality

### Best Practices
- Component composition over inheritance
- Custom hooks for shared logic
- Error boundaries for fault tolerance
- Loading states for better UX

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Email: <EMAIL>
- Phone: (*************
- Address: Brgy. Cabuyan, Tanauan, Leyte

---

**Tanauan School of Arts and Trade**  
*Empowering students through quality education and innovative technology*
