import Link from "next/link"
import { Home, Search } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
      <Card className="w-full max-w-md text-center">
        <CardHeader>
          <div className="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
            <Search className="h-8 w-8 text-gray-600" />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
            404 - Page Not Found
          </CardTitle>
          <CardDescription>
            The page you're looking for doesn't exist in the TSAT Attendance System.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            This could be because:
          </p>
          <ul className="text-sm text-gray-600 dark:text-gray-400 text-left space-y-1">
            <li>• The URL was typed incorrectly</li>
            <li>• The page has been moved or deleted</li>
            <li>• You don't have permission to access this page</li>
          </ul>
          <div className="flex gap-2 pt-4">
            <Button asChild className="flex-1">
              <Link href="/">
                <Home className="h-4 w-4 mr-2" />
                Go Home
              </Link>
            </Button>
            <Button variant="outline" onClick={() => window.history.back()} className="flex-1">
              Go Back
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
