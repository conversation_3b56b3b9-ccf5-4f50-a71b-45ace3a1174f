import { GraduationCap } from "lucide-react"

interface SchoolLogoProps {
  size?: "sm" | "md" | "lg"
  showText?: boolean
  className?: string
}

export function SchoolLogo({ size = "md", showText = true, className = "" }: SchoolLogoProps) {
  const sizeClasses = {
    sm: "h-6 w-6",
    md: "h-8 w-8", 
    lg: "h-12 w-12"
  }

  const textSizeClasses = {
    sm: "text-sm",
    md: "text-lg",
    lg: "text-2xl"
  }

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      <div className="education-gradient p-2 rounded-lg">
        <GraduationCap className={`${sizeClasses[size]} text-white`} />
      </div>
      {showText && (
        <div>
          <h2 className={`${textSizeClasses[size]} font-bold text-gray-900 dark:text-white`}>
            TSAT
          </h2>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Attendance System
          </p>
        </div>
      )}
    </div>
  )
}

export function SchoolHeader() {
  return (
    <div className="text-center py-8 bg-gradient-to-r from-education-blue-600 to-education-green-600 text-white">
      <SchoolLogo size="lg" className="justify-center mb-4" />
      <h1 className="text-3xl font-bold mb-2">
        Tanauan School of Arts and Trade
      </h1>
      <p className="text-blue-100 text-lg">
        Brgy. Cabuyan, Tanauan, Leyte
      </p>
      <p className="text-blue-200 text-sm mt-2">
        QR-Code Based Student Attendance and Monitoring System
      </p>
    </div>
  )
}

export function SchoolFooter() {
  return (
    <footer className="bg-gray-900 text-white py-8 mt-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <SchoolLogo size="md" className="mb-4" />
            <p className="text-gray-400 text-sm">
              Empowering students through quality education and innovative technology solutions.
            </p>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact Information</h3>
            <div className="space-y-2 text-sm text-gray-400">
              <p>Brgy. Cabuyan, Tanauan, Leyte</p>
              <p>Phone: (*************</p>
              <p>Email: <EMAIL></p>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <div className="space-y-2 text-sm text-gray-400">
              <p>Academic Calendar</p>
              <p>Student Portal</p>
              <p>Faculty Directory</p>
              <p>Support Center</p>
            </div>
          </div>
        </div>
        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-sm text-gray-400">
          <p>&copy; 2024 Tanauan School of Arts and Trade. All rights reserved.</p>
        </div>
      </div>
    </footer>
  )
}
