"use client"

import { MainLayout } from "@/components/layout/main-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Scan, Camera, History, CheckCircle, XCircle, Clock } from "lucide-react"
import { useState } from "react"

const recentScans = [
  {
    id: "1",
    studentName: "Juan Dela Cruz",
    studentId: "2021-001234",
    course: "BSIT 3A",
    time: "8:15 AM",
    status: "success",
  },
  {
    id: "2",
    studentName: "Maria Santos",
    studentId: "2021-001235",
    course: "BSBA 2B",
    time: "8:12 AM",
    status: "success",
  },
  {
    id: "3",
    studentName: "Invalid QR Code",
    studentId: "Unknown",
    course: "-",
    time: "8:10 AM",
    status: "error",
  },
]

export default function ScanPage() {
  const [isScanning, setIsScanning] = useState(false)
  const [scanResult, setScanResult] = useState<string | null>(null)

  const handleStartScan = () => {
    setIsScanning(true)
    // Simulate scanning process
    setTimeout(() => {
      setIsScanning(false)
      setScanResult("Student checked in successfully!")
    }, 2000)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "error":
        return <XCircle className="h-4 w-4 text-red-600" />
      default:
        return <Clock className="h-4 w-4 text-yellow-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "success":
        return "bg-green-50 dark:bg-green-900 border-green-200 dark:border-green-700"
      case "error":
        return "bg-red-50 dark:bg-red-900 border-red-200 dark:border-red-700"
      default:
        return "bg-yellow-50 dark:bg-yellow-900 border-yellow-200 dark:border-yellow-700"
    }
  }

  return (
    <MainLayout title="Scan QR Code">
      <div className="space-y-6">
        {/* Scanner Section */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Scan className="h-5 w-5" />
                QR Code Scanner
              </CardTitle>
              <CardDescription>
                Scan student QR codes to record attendance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="aspect-square bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-gray-600">
                {isScanning ? (
                  <div className="text-center">
                    <Camera className="h-12 w-12 mx-auto mb-4 text-blue-600 animate-pulse" />
                    <p className="text-sm text-muted-foreground">Scanning...</p>
                  </div>
                ) : (
                  <div className="text-center">
                    <Scan className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                    <p className="text-sm text-muted-foreground">
                      Camera preview will appear here
                    </p>
                  </div>
                )}
              </div>
              
              <Button 
                onClick={handleStartScan}
                disabled={isScanning}
                className="w-full"
                variant="education"
              >
                {isScanning ? "Scanning..." : "Start Scanning"}
              </Button>

              {scanResult && (
                <div className="p-3 bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg">
                  <p className="text-sm text-green-800 dark:text-green-200">
                    {scanResult}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Scan Statistics */}
          <Card>
            <CardHeader>
              <CardTitle>Today's Scan Statistics</CardTitle>
              <CardDescription>
                Overview of scanning activity
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-green-50 dark:bg-green-900 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">156</div>
                  <div className="text-sm text-muted-foreground">Successful</div>
                </div>
                <div className="text-center p-4 bg-red-50 dark:bg-red-900 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">3</div>
                  <div className="text-sm text-muted-foreground">Failed</div>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Success Rate</span>
                  <span className="font-medium">98.1%</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div className="bg-green-600 h-2 rounded-full" style={{ width: "98.1%" }}></div>
                </div>
              </div>

              <div className="pt-4 border-t">
                <div className="text-sm text-muted-foreground mb-2">Scan Session</div>
                <div className="text-lg font-semibold">Started at 7:30 AM</div>
                <div className="text-sm text-muted-foreground">Duration: 45 minutes</div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Scans */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <History className="h-5 w-5" />
              Recent Scans
            </CardTitle>
            <CardDescription>
              Latest QR code scan results
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentScans.map((scan) => (
                <div 
                  key={scan.id} 
                  className={`p-4 rounded-lg border ${getStatusColor(scan.status)}`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(scan.status)}
                      <div>
                        <div className="font-medium">{scan.studentName}</div>
                        <div className="text-sm text-muted-foreground">
                          {scan.studentId} • {scan.course}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">{scan.time}</div>
                      <div className="text-xs text-muted-foreground">
                        {scan.status === "success" ? "Checked In" : "Failed"}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
