"use client"

import { MainLayout } from "@/components/layout/main-layout"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { BarChart3, TrendingUp, Calendar, Users, Clock, Target } from "lucide-react"

const weeklyData = [
  { day: "Mon", attendance: 92 },
  { day: "Tue", attendance: 88 },
  { day: "Wed", attendance: 95 },
  { day: "Thu", attendance: 89 },
  { day: "Fri", attendance: 91 },
]

const courseData = [
  { course: "BSIT", students: 456, attendance: 92 },
  { course: "BSBA", students: 389, attendance: 88 },
  { course: "BSED", students: 234, attendance: 94 },
  { course: "BSCS", students: 155, attendance: 90 },
]

export default function AnalyticsPage() {
  return (
    <MainLayout title="Analytics">
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Attendance Analytics
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Comprehensive insights into student attendance patterns
          </p>
        </div>

        {/* Key Metrics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Overall Attendance</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">89.2%</div>
              <p className="text-xs text-muted-foreground">
                +2.1% from last month
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Daily Average</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">1,089</div>
              <p className="text-xs text-muted-foreground">
                Students per day
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Peak Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">8:15 AM</div>
              <p className="text-xs text-muted-foreground">
                Most check-ins
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Trend</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">↑ 3.2%</div>
              <p className="text-xs text-muted-foreground">
                This week vs last
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Charts Section */}
        <div className="grid gap-6 md:grid-cols-2">
          {/* Weekly Attendance Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Weekly Attendance Trend
              </CardTitle>
              <CardDescription>
                Attendance percentage by day of the week
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {weeklyData.map((day) => (
                  <div key={day.day} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-12 text-sm font-medium">{day.day}</div>
                      <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 w-32">
                        <div 
                          className="bg-education-blue-600 h-2 rounded-full" 
                          style={{ width: `${day.attendance}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="text-sm font-medium">{day.attendance}%</div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Course-wise Performance */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Course-wise Performance
              </CardTitle>
              <CardDescription>
                Attendance rates by academic program
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {courseData.map((course) => (
                  <div key={course.course} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="font-medium">{course.course}</div>
                        <div className="text-sm text-muted-foreground">
                          {course.students} students
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">{course.attendance}%</div>
                      </div>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div 
                        className="bg-education-green-600 h-2 rounded-full" 
                        style={{ width: `${course.attendance}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Analytics */}
        <div className="grid gap-6 md:grid-cols-3">
          <Card>
            <CardHeader>
              <CardTitle>Attendance Patterns</CardTitle>
              <CardDescription>
                Common attendance behaviors
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center p-3 bg-green-50 dark:bg-green-900 rounded-lg">
                <div>
                  <div className="font-medium">Regular Attendees</div>
                  <div className="text-sm text-muted-foreground">90%+ attendance</div>
                </div>
                <div className="text-2xl font-bold text-green-600">856</div>
              </div>
              <div className="flex justify-between items-center p-3 bg-yellow-50 dark:bg-yellow-900 rounded-lg">
                <div>
                  <div className="font-medium">Occasional Absences</div>
                  <div className="text-sm text-muted-foreground">70-89% attendance</div>
                </div>
                <div className="text-2xl font-bold text-yellow-600">289</div>
              </div>
              <div className="flex justify-between items-center p-3 bg-red-50 dark:bg-red-900 rounded-lg">
                <div>
                  <div className="font-medium">At Risk</div>
                  <div className="text-sm text-muted-foreground">&lt;70% attendance</div>
                </div>
                <div className="text-2xl font-bold text-red-600">89</div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Time Analysis</CardTitle>
              <CardDescription>
                Check-in time distribution
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div>
                  <div className="flex justify-between text-sm">
                    <span>7:00 - 8:00 AM</span>
                    <span className="font-medium">45%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div className="bg-education-blue-600 h-2 rounded-full" style={{ width: "45%" }}></div>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between text-sm">
                    <span>8:00 - 9:00 AM</span>
                    <span className="font-medium">35%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div className="bg-education-blue-600 h-2 rounded-full" style={{ width: "35%" }}></div>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between text-sm">
                    <span>9:00 - 10:00 AM</span>
                    <span className="font-medium">15%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div className="bg-education-blue-600 h-2 rounded-full" style={{ width: "15%" }}></div>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between text-sm">
                    <span>After 10:00 AM</span>
                    <span className="font-medium">5%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div className="bg-red-600 h-2 rounded-full" style={{ width: "5%" }}></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Monthly Comparison</CardTitle>
              <CardDescription>
                Current vs previous month
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">89.2%</div>
                <div className="text-sm text-muted-foreground">This Month</div>
              </div>
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="text-2xl font-bold text-gray-600">87.1%</div>
                <div className="text-sm text-muted-foreground">Last Month</div>
              </div>
              <div className="text-center p-4 bg-green-50 dark:bg-green-900 rounded-lg">
                <div className="text-2xl font-bold text-green-600">+2.1%</div>
                <div className="text-sm text-muted-foreground">Improvement</div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  )
}
