"use client"

import { MainLayout } from "@/components/layout/main-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Users, UserCheck, UserX, Clock, TrendingUp, Calendar } from "lucide-react"

const stats = [
  {
    title: "Total Students",
    value: "1,234",
    description: "Active enrolled students",
    icon: Users,
    color: "text-blue-600",
    bgColor: "bg-blue-100 dark:bg-blue-900",
  },
  {
    title: "Present Today",
    value: "1,089",
    description: "88.2% attendance rate",
    icon: UserCheck,
    color: "text-green-600",
    bgColor: "bg-green-100 dark:bg-green-900",
  },
  {
    title: "Absent Today",
    value: "145",
    description: "11.8% of total students",
    icon: UserX,
    color: "text-red-600",
    bgColor: "bg-red-100 dark:bg-red-900",
  },
  {
    title: "Late Arrivals",
    value: "23",
    description: "Within grace period",
    icon: Clock,
    color: "text-yellow-600",
    bgColor: "bg-yellow-100 dark:bg-yellow-900",
  },
]

const recentActivity = [
  {
    student: "Juan Del<PERSON> Cruz",
    action: "Checked in",
    time: "8:15 AM",
    course: "BSIT 3A",
  },
  {
    student: "Maria <PERSON>",
    action: "Checked in",
    time: "8:12 AM",
    course: "BSBA 2B",
  },
  {
    student: "Pedro Garcia",
    action: "Checked in",
    time: "8:10 AM",
    course: "BSIT 4A",
  },
  {
    student: "Ana Rodriguez",
    action: "Checked in",
    time: "8:08 AM",
    course: "BSBA 1A",
  },
]

export default function DashboardPage() {
  return (
    <MainLayout title="Dashboard">
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="education-gradient rounded-lg p-6 text-white">
          <h1 className="text-2xl font-bold mb-2">
            Welcome to TSAT Attendance System
          </h1>
          <p className="text-blue-100">
            Monitor student attendance efficiently with QR code technology
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat) => (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <div className={`p-2 rounded-full ${stat.bgColor}`}>
                  <stat.icon className={`h-4 w-4 ${stat.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {/* Recent Activity */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Recent Activity
              </CardTitle>
              <CardDescription>
                Latest student check-ins and attendance updates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-gray-800">
                    <div>
                      <p className="font-medium">{activity.student}</p>
                      <p className="text-sm text-muted-foreground">
                        {activity.course}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-green-600">
                        {activity.action}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {activity.time}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Quick Actions
              </CardTitle>
              <CardDescription>
                Common tasks and shortcuts
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <button className="w-full p-3 text-left rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                <div className="font-medium">Scan QR Code</div>
                <div className="text-sm text-muted-foreground">
                  Start attendance scanning
                </div>
              </button>
              <button className="w-full p-3 text-left rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                <div className="font-medium">View Reports</div>
                <div className="text-sm text-muted-foreground">
                  Generate attendance reports
                </div>
              </button>
              <button className="w-full p-3 text-left rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                <div className="font-medium">Manage Students</div>
                <div className="text-sm text-muted-foreground">
                  Add or edit student records
                </div>
              </button>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  )
}
